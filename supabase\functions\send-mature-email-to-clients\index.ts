import { serve } from 'https://deno.land/std@0.190.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.39.7';
import nodemailer from 'npm:nodemailer';
serve(async (req)=>{
  // 1. Setup Supabase client
  const supabase = createClient(Deno.env.get('SUPABASE_URL'), Deno.env.get('SUPABASE_SERVICE_ROLE_KEY'));
  // 2. Fetch notification settings
  const { data: settings, error: settingsError } = await supabase.from('notification_settings').select('*').single();
  if (settingsError || !settings?.email_enabled) {
    return new Response(JSON.stringify({
      error: 'Email notifications disabled or settings error'
    }), {
      status: 400
    });
  }
  // 3. Calculate target date
  const daysBefore = settings.days_before_maturity_email || 3;
  const targetDate = new Date();
  targetDate.setDate(targetDate.getDate() + daysBefore);
  const targetDateStr = targetDate.toISOString().split('T')[0];
  // 4. Query investments maturing in X days
  const { data: investments, error: invError } = await supabase.from('investments').select(`
      id,
      maturity_date,
      scheme_name,
      primary_applicant_cif_id,
      secondary_applicant_cif_id
    `).eq('maturity_date', targetDateStr).eq('status', 'active');
  if (invError) {
    return new Response(JSON.stringify({
      error: 'Failed to fetch investments'
    }), {
      status: 500
    });
  }
  // 5. Collect all unique CIF IDs for primary and secondary applicants
  const cifIds = new Set();
  for (const inv of investments || []){
    if (inv.primary_applicant_cif_id) cifIds.add(inv.primary_applicant_cif_id);
    if (inv.secondary_applicant_cif_id) cifIds.add(inv.secondary_applicant_cif_id);
  }
  // 6. Fetch client emails for all relevant CIF IDs
  const { data: clients, error: clientsError } = await supabase.from('clients').select('cif_id, email, first_name, last_name').in('cif_id', Array.from(cifIds));
  if (clientsError) {
    return new Response(JSON.stringify({
      error: 'Failed to fetch client emails'
    }), {
      status: 500
    });
  }
  // 7. Map CIF ID to client info
  const clientMap = new Map();
  for (const c of clients || []){
    if (c.email) {
      clientMap.set(c.cif_id, {
        email: c.email,
        name: `${c.first_name || ''} ${c.last_name || ''}`.trim()
      });
    }
  }
  // 8. Setup nodemailer transporter
  const transporter = nodemailer.createTransport({
    host: 'smtp.gmail.com',
    port: 587,
    secure: false,
    auth: {
      user: '<EMAIL>',
      pass: 'pkhjogzbldwssfvf'
    }
  });
  // 9. Send emails
  let sent = 0;
  let errors = [];
  for (const inv of investments || []){
    const recipients = [];
    if (inv.primary_applicant_cif_id && clientMap.has(inv.primary_applicant_cif_id)) {
      recipients.push(clientMap.get(inv.primary_applicant_cif_id));
    }
    if (inv.secondary_applicant_cif_id && clientMap.has(inv.secondary_applicant_cif_id) && inv.secondary_applicant_cif_id !== inv.primary_applicant_cif_id) {
      recipients.push(clientMap.get(inv.secondary_applicant_cif_id));
    }
    for (const recipient of recipients){
      try {
        await transporter.sendMail({
          from: settings.smtp_username,
          to: recipient.email,
          subject: `Your investment in ${inv.scheme_name} is maturing soon`,
          html: `<p>Dear ${recipient.name || 'Investor'},<br>Your investment in <b>${inv.scheme_name}</b> will mature on <b>${inv.maturity_date}</b>.</p>`
        });
        sent++;
      } catch (err) {
        errors.push({
          email: recipient.email,
          error: err.message
        });
      }
    }
  }
  return new Response(JSON.stringify({
    success: true,
    sent,
    errors
  }), {
    status: 200,
    headers: {
      'Content-Type': 'application/json'
    }
  });
});
